import 'package:flutter/material.dart';
import 'package:k_chart_plus/chart_style.dart';
import 'package:k_chart_plus/extension/map_ext.dart';
import '../entity/k_line_entity.dart';
import '../utils/date_format_util.dart';
import '../utils/number_util.dart';

class PopupInfoView extends StatelessWidget {
  final KLineEntity entity;
  final double width;
  final ChartColors chartColors;
  final bool materialInfoDialog;
  final List<String> timeFormat;
  final int fixedLength;
  final bool isLine;
  final double? closePrice;
  final String? locale;
  final Color Function(double value)? getColorCallback;
  final double? gainPercent;
  final double? changeAmount;
  const PopupInfoView({
    super.key,
    required this.entity,
    required this.width,
    required this.chartColors,
    required this.materialInfoDialog,
    required this.timeFormat,
    required this.fixedLength,
    this.locale,
    this.isLine = false,
    this.closePrice,
    this.getColorCallback,
    this.gainPercent,
    this.changeAmount,
  });

  @override
  Widget build(BuildContext context) {
    //! h5
    // return DecoratedBox(
    //   decoration: BoxDecoration(
    //     color: chartColors.selectFillColor,
    //     border: Border.all(color: chartColors.selectBorderColor, width: 0.5),
    //   ),
    //   child: SizedBox(
    //     width: width,
    //     child: Padding(
    //       padding: const EdgeInsets.fromLTRB(6.0, 6.0, 6.0, 0.0),
    //       child: _buildBody(context),
    //     ),
    //   ),
    // );
    return DecoratedBox(
      decoration: BoxDecoration(
        color: chartColors.selectFillColor,
      ),
      child: SizedBox(
        // width: width,
        child: _buildBody(context, getColorCallback, closePrice),
      ),
    );
  }

  Widget _buildBody(BuildContext context, Color Function(double value)? getColorCallback, closePrice) {
    double upDown = (entity.change ?? (entity.amount ?? 0) - (closePrice ?? 0));
    // Use gain percentage if provided, otherwise calculate
    double upDownPercent = (entity.ratio ?? (closePrice != null && closePrice != 0 ? (upDown / closePrice) * 100 : 0));
    final double? entityAmount = entity.amount;

    //! h5
    // final isGreen = upDown <= 0;

    if (isLine) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            spacing: 8,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildNewColorItem('price'.translate(locale), (entity.amount ?? 0).toStringAsFixed(fixedLength),
                  getColorCallback?.call(entity.amount ?? 0) ?? Colors.transparent),
              _buildColorItem(
                'changeAmount'.translate(locale),
                (upDown > 0 ? "+" : "") + upDown.toStringAsFixed(fixedLength),
                upDown > 0,
              ),
              _buildColorItem(
                'change'.translate(locale),
                "${upDownPercent.toStringAsFixed(2)}%",
                upDownPercent > 0,
              ),
            ],
          ),
          Row(
            spacing: 8,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildNewColorItem(
                'avgPrice'.translate(locale),
                entity.open.toStringAsFixed(fixedLength),
                getColorCallback?.call(entity.open) ?? Colors.transparent,
                isYellow: true,
              ),
              if (entityAmount != null)
                _buildItem('amount'.translate(locale),
                    NumberUtil.formatLargeNumber(entity.vol * (entity.amount ?? 0), locale)),
              _buildItem('vol'.translate(locale), NumberUtil.formatLargeNumber(entity.vol, locale)),
            ],
          ),
        ],
      );
    }
    return Row(
      spacing: 8,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildNewColorItem(
          'open'.translate(locale),
          entity.open.toStringAsFixed(fixedLength),
          getColorCallback?.call(entity.open) ?? Colors.transparent,
        ),
        _buildNewColorItem('high'.translate(locale), entity.high.toStringAsFixed(fixedLength),
            getColorCallback?.call(entity.high) ?? Colors.transparent),
        _buildNewColorItem('low'.translate(locale), entity.low.toStringAsFixed(fixedLength),
            getColorCallback?.call(entity.low) ?? Colors.transparent),
        _buildNewColorItem('close'.translate(locale), entity.close.toStringAsFixed(fixedLength),
            getColorCallback?.call(entity.close) ?? Colors.transparent),
        if (entityAmount != null)
          _buildItem(
              'amount'.translate(locale), NumberUtil.formatLargeNumber(entity.vol * (entity.amount ?? 0), locale)),
        _buildItem('vol'.translate(locale), NumberUtil.formatLargeNumber(entity.vol, locale)),
      ],
    );
  }

  Widget _buildColorItem(String label, String info, bool isUp) {
    if (isUp) {
      return _buildItem(
        label,
        info,
        textColor: chartColors.infoWindowDnColor,
      );
    }
    return _buildItem(label, info, textColor: chartColors.infoWindowUpColor);
  }

  Widget _buildNewColorItem(String label, String info, Color color, {bool isYellow = false}) {
    if (color == Colors.transparent) {
      return _buildItem(
        label,
        info,
        textColor: isYellow ? chartColors.trendLineColor : chartColors.infoWindowUpColor,
      );
    }
    return _buildItem(label, info, textColor: chartColors.infoWindowDnColor);
  }

  Widget _buildItem(String label, String info, {Color? textColor}) {
    final infoWidget = Row(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: <Widget>[
        Text(
          '$label: ',
          style: TextStyle(
            color: chartColors.infoWindowTitleColor,
            fontSize: 10.0,
            fontFamily: 'PingFang',
            letterSpacing: -0.3,
          ),
        ),
        Text(
          info,
          style: TextStyle(
              color: textColor ?? chartColors.infoWindowNormalColor, fontSize: 10.0, fontFamily: 'Akzidenz-Grotesk'),
          textAlign: TextAlign.right,
        ),
      ],
    );
    return materialInfoDialog ? Material(color: Colors.transparent, child: infoWidget) : infoWidget;
  }

  String getDate(int? date) => dateFormat(
        DateTime.fromMillisecondsSinceEpoch(
          date ?? DateTime.now().millisecondsSinceEpoch,
        ),
        timeFormat,
      );
}

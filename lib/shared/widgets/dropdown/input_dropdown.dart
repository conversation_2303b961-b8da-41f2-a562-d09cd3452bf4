import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/custom_number_picker.dart';

import '../debouncer.dart';

class InputDropdownWidget<T> extends StatefulWidget {
  /// The list of dropdown items
  final List<T> items;

  /// Optional hint text for the dropdown
  final String? hintText;

  /// Current selected value
  final T? selectedValue;

  /// Callback when a dropdown item is selected
  final ValueChanged<T?>? onDropdownChanged;

  /// Callback when text is entered
  final ValueChanged<String>? onTextChanged;

  /// Item builder for customizing the dropdown items
  final Widget Function(T) itemBuilder;

  final TextEditingController textController;

  /// Title for the item picker
  final String? itemPickerTitle;

  const InputDropdownWidget({
    super.key,
    required this.items,
    this.hintText,
    this.selectedValue,
    this.onDropdownChanged,
    this.onTextChanged,
    this.itemPickerTitle,
    required this.itemBuilder,
    required this.textController,
  });

  @override
  InputDropdownWidgetState<T> createState() => InputDropdownWidgetState<T>();
}

class InputDropdownWidgetState<T> extends State<InputDropdownWidget<T>> {
  T? _selectedValue;
  late final Debouncer _debouncer;

  @override
  void initState() {
    super.initState();
    widget.textController.text = widget.selectedValue?.toString() ?? '';
    _selectedValue = widget.selectedValue;
    _debouncer = Debouncer(milliseconds: 400);
  }

  @override
  void didUpdateWidget(InputDropdownWidget<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update if the selected value actually changed and text controller is different
    if (oldWidget.selectedValue != widget.selectedValue) {
      final newText = widget.selectedValue?.toString() ?? '';
      if (widget.textController.text != newText) {
        widget.textController.text = newText;
      }
      _selectedValue = widget.selectedValue;
    }
  }

  @override
  void dispose() {
    _debouncer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final fillColor =
        isDark ? Theme.of(context).inputDecorationTheme.fillColor : Theme.of(context).inputDecorationTheme.fillColor;
    return TextField(
      controller: widget.textController,
      decoration: InputDecoration(
        floatingLabelBehavior: FloatingLabelBehavior.never,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5.gr),
          borderSide: BorderSide(color: context.theme.primaryColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5.gr),
          borderSide: const BorderSide(color: Colors.transparent),
        ),
        filled: true,
        fillColor: fillColor,
        hintText: widget.hintText,
        hintStyle: context.textTheme.regular.fs13,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5.gr),
          borderSide: BorderSide(color: context.theme.primaryColor.withAlpha(128)),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.0),
        suffixIcon: IconButton(
          icon: Icon(Icons.keyboard_arrow_down_sharp, size: 15.gr, color: context.colorTheme.textPrimary),
          onPressed: () async {
            final selectedValue = await CustomNumberPicker.show<T>(
              context,
              numbers: widget.items,
              initialValue: _selectedValue,
              title: widget.itemPickerTitle, // Optional title
              itemBuilder: widget.itemBuilder,
            );

            if (selectedValue != null) {
              setState(() {
                _selectedValue = selectedValue;
              });

              // Update text controller to reflect the selected value
              widget.textController.text = selectedValue.toString();

              // Call onDropdownChanged if provided
              widget.onDropdownChanged?.call(selectedValue);

            }
          },
        ),
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
      ],
      onChanged: (value) {
        _debouncer.run(() {
          widget.onTextChanged?.call(value);
        });
      },
      style: context.textTheme.primary.fs13.ffAkz,
    );
  }
}

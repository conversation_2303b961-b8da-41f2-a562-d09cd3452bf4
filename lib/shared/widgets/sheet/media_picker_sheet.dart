import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

enum PickerType {
  camera,    // 相机
  gallery,   // 相册
  file       // 文件
}

class MediaPickerSheet extends StatelessWidget {
  final Future<List<String>> Function() onCamera;
  final Future<List<String>> Function() onGallery;
  final Future<List<String>> Function() onFile;

  const MediaPickerSheet({
    super.key,
    required this.onCamera,
    required this.onGallery,
    required this.onFile,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 20.gw, horizontal: 15.gw),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '选择操作',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 30.gw),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: _buildOptionItem(
                  icon: Icons.camera_alt,
                  title: "相机",
                  onTap: () async {
                    final result = await onCamera();
                    if (context.mounted) Navigator.pop(context, result);
                  },
                ),
              ),
              Expanded(
                child: _buildOptionItem(
                  icon: Icons.photo_library,
                  title: "相册",
                  onTap: () async {
                    final result = await onGallery();
                    if (context.mounted) Navigator.pop(context, result);
                  },
                ),
              ),
              Expanded(
                child: _buildOptionItem(
                  icon: Icons.file_copy,
                  title: "媒体选择工具",
                  onTap: () async {
                    final result = await onFile();
                    if (context.mounted) Navigator.pop(context, result);
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 20.gw),
        ],
      ),
    );
  }

  Widget _buildOptionItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60.gw,
            height: 60.gw,
            decoration: BoxDecoration(
              color: const Color(0xFFE91E63),
              borderRadius: BorderRadius.circular(15.gw),
            ),
            child: Icon(
              icon,
              size: 30.gw,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 8.gw),
          Text(
            title,
            style: TextStyle(
              fontSize: 14.gw,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}
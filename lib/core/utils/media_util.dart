import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:image/image.dart' as image;
import 'package:image_picker/image_picker.dart' as image_picker;
import 'package:path_provider/path_provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

// 定义媒体类型枚举
enum MediaType {
  image,
  video,
  all,
}

class MediaUtil {
  /// 检查媒体权限
  static Future<bool> _checkMediaPermission(MediaType type) async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt >= 33) {
        Permission permission;
        switch (type) {
          case MediaType.image:
            permission = Permission.photos;
            break;
          case MediaType.video:
            permission = Permission.videos;
            break;
          case MediaType.all:
            // 同时需要照片和视频权限的情况
            final photos = await Permission.photos.request();
            final videos = await Permission.videos.request();
            // 检查两个权限是否都被授予
            if (!photos.isGranted || !videos.isGranted) {
              GPEasyLoading.showToast("请授予媒体访问权限");
              await openAppSettings();
              return false;
            }
            return true;
        }

        final status = await permission.status;
        if (status.isDenied) {
          final result = await permission.request();
          if (result.isDenied) {
            GPEasyLoading.showToast("请授予媒体访问权限");
            await openAppSettings();
            return false;
          }
        }
        return true;
      }
    }

    // Android 13 以下或其他平台使用存储权限
    final status = await Permission.storage.status;
    if (status.isDenied) {
      final result = await Permission.storage.request();
      if (result.isDenied) {
        GPEasyLoading.showToast("请授予存储访问权限");
        return false;
      }
    }
    return true;
  }

  /// 处理图片选择
  /// [imageQuality] 图片质量 1-100
  /// [maxWidth] 图片最大宽度
  /// 返回值为图片URI字符串列表
  static Future<List<String>> handleImagePicker({
    int? imageQuality,
    int? maxWidth,
  }) async {
    // 只检查图片权限
    if (!await _checkMediaPermission(MediaType.image)) {
      return [];
    }
    final picker = image_picker.ImagePicker();
    final media = await picker.pickImage(
      source: image_picker.ImageSource.gallery, // 从图库选择
      imageQuality: imageQuality ?? 90,
    );

    if (media == null) return [];

    // 处理图片尺寸和质量
    final imageData = await media.readAsBytes();
    final decodedImage = image.decodeImage(imageData)!;
    final scaledImage = image.copyResize(
      decodedImage,
      width: maxWidth ?? 500, // 默认最大宽度500
    );
    final jpg = image.encodeJpg(scaledImage, quality: imageQuality ?? 90);

    // 保存处理后的图片到临时目录
    final filePath = (await getTemporaryDirectory()).uri.resolve(
          './image_${DateTime.now().microsecondsSinceEpoch}.jpg',
        );
    final file = await File.fromUri(filePath).create(recursive: true);
    await file.writeAsBytes(jpg, flush: true);

    return [file.uri.toString()];
  }

  /// 处理视频选择
  /// [maxSizeMB] 视频文件最大大小（MB）
  /// 返回值为视频URI字符串列表
  static Future<List<String>> handleVideoPicker({
    int? maxSizeMB,
  }) async {
    // 只检查视频权限
    if (!await _checkMediaPermission(MediaType.video)) {
      return [];
    }
    final picker = image_picker.ImagePicker();
    final media = await picker.pickVideo(
      source: image_picker.ImageSource.gallery, // 从图库选择
    );

    if (media == null) return [];

    // 检查视频文件大小
    final file = File(media.path);
    final fileSizeInMB = await file.length() / (1024 * 1024);

    if (maxSizeMB != null && fileSizeInMB > maxSizeMB) {
      GPEasyLoading.showToast("注意文件大小不能超过${maxSizeMB}MB");
      return [];
    }

    return [file.uri.toString()];
  }

  /// 处理文件选择
  /// [allowedExtensions] 允许的文件扩展名列表，例如 ['pdf', 'doc']
  /// [type] 文件类型，默认为任意类型
  /// [maxSizeMB] 文件最大大小（MB）
  /// 返回值为文件URI字符串列表
  static Future<List<String>> handleFilePicker({
    List<String>? allowedExtensions,
    FileType type = FileType.any,
    int? maxSizeMB,
  }) async {
    // 选择文件
    final filesResult = await FilePicker.platform.pickFiles(
      type: type,
      allowedExtensions: allowedExtensions,
    );

    if (filesResult != null && filesResult.files.single.path != null) {
      final file = File(filesResult.files.single.path!);

      // 检查文件大小
      if (maxSizeMB != null) {
        final fileSizeInMB = await file.length() / (1024 * 1024);
        if (fileSizeInMB > maxSizeMB) {
          GPEasyLoading.showToast("注意文件大小不能超过${maxSizeMB}MB");
          return [];
        }
      }

      return [file.uri.toString()];
    }

    return [];
  }

  /// 同时处理图片和视频选择
  /// [maxAssets] 最大可选择数量
  /// [requestType] 支持的类型，默认图片和视频都支持
  /// 返回值为媒体文件URI字符串列表
  static Future<List<String>> handleMediaPicker(
    context, {
    int maxAssets = 9,
    RequestType requestType = RequestType.common,
  }) async {
    // 需要同时检查图片和视频权限
    if (!await _checkMediaPermission(MediaType.all)) {
      return [];
    }
    final List<AssetEntity>? result = await AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        maxAssets: maxAssets,
        requestType: requestType,
        textDelegate: const AssetPickerTextDelegate(),
      ),
    );

    if (result == null) return [];

    List<String> uris = [];
    for (var asset in result) {
      final file = await asset.file;
      if (file != null) {
        uris.add(file.uri.toString());
      }
    }

    return uris;
  }

  /// 打开相机进行拍摄（支持照片和视频）
  /// [imageQuality] 图片质量 1-100（仅图片时有效）
  /// [maxWidth] 图片最大宽度（仅图片时有效）
  /// [maxSizeMB] 视频文件最大大小（MB）（仅视频时有效）
  /// 返回值为文件URI字符串列表
  static Future<List<String>> handleCameraCapture(
    context, {
    int? imageQuality,
    int? maxWidth,
    int? maxSizeMB,
  }) async {
    // 检查相机权限
    final cameraStatus = await Permission.camera.status;
    if (cameraStatus.isDenied) {
      final result = await Permission.camera.request();
      if (result.isDenied) {
        GPEasyLoading.showToast("请授予相机访问权限");
        await openAppSettings();
        return [];
      }
    }

    final AssetEntity? entity = await CameraPicker.pickFromCamera(
      context,
      pickerConfig: const CameraPickerConfig(
        enableRecording: true, // 启用视频录制
        enableAudio: true, // 启用音频录制
        textDelegate: CameraPickerTextDelegate(),
      ),
    );

    if (entity == null) return [];

    final file = await entity.file;
    if (file == null) return [];

    // 根据资产类型处理文件
    if (entity.type == AssetType.image) {
      // 处理图片尺寸和质量
      final imageData = await file.readAsBytes();
      final decodedImage = image.decodeImage(imageData)!;
      final scaledImage = image.copyResize(
        decodedImage,
        width: maxWidth ?? 500, // 默认最大宽度500
      );
      final jpg = image.encodeJpg(scaledImage, quality: imageQuality ?? 90);

      // 保存处理后的图片到临时目录
      final filePath = (await getTemporaryDirectory()).uri.resolve(
            './image_${DateTime.now().microsecondsSinceEpoch}.jpg',
          );
      final processedFile = await File.fromUri(filePath).create(recursive: true);
      await processedFile.writeAsBytes(jpg, flush: true);
      return [processedFile.uri.toString()];
    } else if (entity.type == AssetType.video) {
      // 检查视频文件大小
      final fileSizeInMB = await file.length() / (1024 * 1024);
      if (maxSizeMB != null && fileSizeInMB > maxSizeMB) {
        GPEasyLoading.showToast("注意文件大小不能超过${maxSizeMB}MB");
        return [];
      }
      return [file.uri.toString()];
    }

    return [];
  }
}

import 'package:flutter/foundation.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/utils/extension/future_list_extensions.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';

import 'log.dart';

class HostUtil {
  // 单例模式
  static final HostUtil _instance = HostUtil._internal();

  factory HostUtil() => _instance;

  HostUtil._internal() {
    _initCurrentHost();
  }

  List<String> hostList = [];

  String? currentHost;

  final _domainChecker = DomainChecker();

  Future<void> _initCurrentHost() async {
    final last = await _domainChecker.loadLastHost();
    if (last != null) {
      currentHost = last;
      LogD("🎯初始化使用上次成功的url: $currentHost");
    } else {
      currentHost = AppConfig.instance.baseUrl;
      LogD("🎯初始化使用默认url: $currentHost");
    }
    _domainChecker.saveLastHost(currentHost!);
  }

  Future<String?> fetchHostFromOss({int timeout = 15}) async {
    if (kIsWeb) return null;

    try {
      // 0. 确保已联网（如国行设备首次启动需授权网络）
      await _domainChecker.waitUntilConnected();

      // 1. 优先从 OSS 拉取最新域名列表（每次都更新）
      LogD("优先从 Oss 获取最新域名列表");
      final ossUrls = AppConfig.instance.ossUrls;
      final domains = await _domainChecker._fetchDomainsFromOss(ossUrls);
      if (domains != null && domains.isNotEmpty) {
        hostList = domains.toSet().toList();
        final remoteHost = await _domainChecker.findFastestDomain(hostList);
        if (remoteHost != null) {
          currentHost = remoteHost;
          return remoteHost;
        }
      }

      // 2. 其次尝试上次可用的域名
      final lastHost = await _domainChecker.checkLastSuccessfulHost();
      if (lastHost != null) {
        currentHost = lastHost;
        return lastHost;
      }

      // 3. 回退到内置域名列表
      LogD("回退到内置域名列表");
      final builtInDomains = getBuiltInDomains();
      hostList = List<String>.from(builtInDomains);
      final fallbackHost = await _domainChecker.findFastestDomain(builtInDomains);
      if (fallbackHost != null) {
        currentHost = fallbackHost;
        return fallbackHost;
      }
    } catch (e) {
      LogD("fetchHostFromOss 中的错误: $e");
    }
    return null;
  }

  List<String> getBuiltInDomains() {
    // 返回内置域名列表
    return [
      AppConfig.instance.baseUrl,
    ];
  }

  void dispose() {
    _domainChecker.dispose();
  }

  /// 主动刷新 OSS 域名列表并返回
  Future<List<String>> refreshOssDomains() async {
    try {
      await _domainChecker.waitUntilConnected();
      final res = await _domainChecker._fetchDomainsFromOss(AppConfig.instance.ossUrls) ?? [];
      hostList = res.toSet().toList();
      return hostList;
    } catch (e) {
      return hostList;
    }
  }

  /// 测速给定域名列表
  Future<List<DomainLatency>> testHosts(List<String> domains) => _domainChecker.measureDomains(domains);

  /// 选择指定域名为当前线路，并持久化
  Future<void> selectHost(String domain) async {
    currentHost = domain;
    Http().resetBaseUrl(domain);
    NetworkProvider().resetBaseUrl(domain);

    await _domainChecker.saveLastHost(domain);
  }
}

class _DomainResult {
  final String domain;
  final bool isSuccess;

  _DomainResult(this.domain, this.isSuccess);

  @override
  String toString() {
    return "domain: $domain, isSuccess: $isSuccess";
  }
}

class DomainChecker {
  String? _lastSuccessfulHost;

  // 持久化存储key
  static final String _lastHostKey = 'last_successful_host_${AppConfig.instance.siteId}_${AppConfig.instance.flavor}';

  Future<String?> loadLastHost() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cached = prefs.getString(_lastHostKey);
      _lastSuccessfulHost = cached;
      LogD("直接读取缓存的上次主机: $_lastSuccessfulHost");
      return cached;
    } catch (e) {
      LogD("加载上次成功的主机失败: $e");
      return "";
    }
  }

  Future<void> saveLastHost(String host) async {
    try {
      _lastSuccessfulHost = host;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastHostKey, host);
      LogD("保存上次成功的主机: $host");
    } catch (e) {
      LogD("保存上次成功的主机失败: $e");
    }
  }

  Future<String?> checkLastSuccessfulHost() async {
    if (_lastSuccessfulHost == null) {
      final lastUrl = await loadLastHost();
      if (lastUrl == null) return null;
    }

    try {
      final result = await _checkDomain(_lastSuccessfulHost!).timeout(const Duration(milliseconds: 1500));

      if (result.isSuccess) {
        LogD("上次成功的主机仍然可用");
        await saveLastHost(_lastSuccessfulHost!); // 更新时间戳
        return _lastSuccessfulHost;
      } else {
        LogD("上次成功的主机不再可用");
        return null;
      }
    } catch (e) {
      LogD("检查上次成功的主机时出错: $e");
      return null;
    }
  }

  // 并发限制数和超时时间
  static const int _maxConcurrent = 3;
  static const Duration _checkTimeout = Duration(milliseconds: 1500);

  Future<String?> findFastestDomain(List<String> domains) async {
    try {
      final result = await _tryNewDomains(domains);
      if (result != null) {
        await saveLastHost(result);
      }
      return result;
    } catch (e) {
      LogD("findFastestDomain 错误: $e");
      return null;
    }
  }

  Future<String?> _tryNewDomains(List<String> domains) async {
    // 只取前几个域名优先尝试
    final priorityDomains = domains.take(_maxConcurrent).toList();
    final backupDomains = domains.skip(_maxConcurrent).toList();

    // 先尝试优先域名组(较短超时)
    final priorityResult = await _tryDomainsWithTimeout(priorityDomains, timeout: const Duration(seconds: 3));
    if (priorityResult != null) {
      return priorityResult;
    }

    // 如果优先组失败,尝试备用组(较长超时)
    if (backupDomains.isNotEmpty) {
      return await _tryDomainsWithTimeout(backupDomains, timeout: const Duration(seconds: 10));
    }
    return null;
  }

  Future<String?> _tryDomainsWithTimeout(List<String> domains, {Duration timeout = const Duration(seconds: 2)}) async {
    try {
      final tasks = domains.map((domain) async {
        try {
          final result = await _checkDomain(domain);
          if (result.isSuccess) {
            LogD("✅ 域名可用: $domain");
            return domain;
          } else {
            LogD("❌ 域名不可用: $domain");
            return null;
          }
        } catch (e) {
          LogE("检查域名失败: $domain");
          return null;
        }
      }).toList();

      // 使用我们封装的 firstNonNullSuccessful 并加上超时处理
      return await tasks.firstNonNullSuccessful().timeout(timeout, onTimeout: () {
        LogD("所有域名检查超时");
        return null;
      });
    } catch (e) {
      LogE("尝试域名列表失败: $e");
      return null;
    }
  }

  /// 测试域名延迟（顺序执行，避免并发干扰）
  Future<List<DomainLatency>> measureDomains(List<String> domains) async {
    final List<DomainLatency> results = [];
    for (final domain in domains) {
      final sw = Stopwatch()..start();
      try {
        final res = await _checkDomain(domain).timeout(_checkTimeout);
        sw.stop();
        results.add(DomainLatency(domain, res.isSuccess ? sw.elapsedMilliseconds / 1000.0 : null, res.isSuccess));
      } catch (_) {
        sw.stop();
        results.add(DomainLatency(domain, null, false));
      }
    }
    return results;
  }

  Future<_DomainResult> _checkDomain(String domain) async {
    final url = '$domain/api/ping';
    LogD("尝试域名: $url");
    try {
      final uri = Uri.parse(url);
      final request = await _client.openUrl('OPTIONS', uri);
      request.headers.add('user-agent', 'Mozilla/5.0');

      final response = await request.close().timeout(_checkTimeout);
      await response.drain();

      return _DomainResult(domain, response.statusCode == 200);
    } catch (e) {
      LogD("检查域名错误: $e");
      return _DomainResult(domain, false);
    }
  }

  // HTTP客户端，用于连接复用
  final _client = HttpClient()
    ..connectionTimeout = const Duration(milliseconds: 1500)
    ..maxConnectionsPerHost = 5;

  // 从OSS获取并解析域名列表
  Future<List<String>?> _fetchDomainsFromOss(List<String> ossUrls) async {
    final List<String> allDomains = [];

    try {
      final result = await ossUrls.map((url) => _fetchSingleOssUrl(url)).toList().firstNonNullSuccessful();
      if (result != null) {
        allDomains.addAll(result);
      }
      return allDomains;
    } catch (e) {
      LogD("All OSS requests failed: $e");
      return null;
    }
  }

  // 从单个OSS URL获取域名列表
  Future<List<String>?> _fetchSingleOssUrl(String url) async {
    try {
      LogD("从 $url 获取域名");

      final response = await http.get(Uri.parse(url)).timeout(const Duration(seconds: 3));

      if (response.statusCode == 200) {
        final List<String> domains = List<String>.from(jsonDecode(response.body)).map((e) {
          // 替换空格
          e = e.replaceAll(' ', '');
          if (!e.startsWith('http')) {
            bool isIP = RegExp(r'^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)'
                    r'(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$')
                .hasMatch(e);

            // 不以 http 开头，若出现ip则直接返回，否则添加随机前缀
            return isIP ? "http://$e" : "https://${generateRandomString(8)}.$e";
          } else {
            // 已经是 http/https，直接返回
            return e;
          }
        }).toList();

        LogD("成功从 $url 获取域名: $domains");
        return domains;
      }

      LogD("从 $url 获取域名失败: ${response.statusCode}");
      return null;
    } catch (e) {
      LogD("从 $url 获取域名时出错: $e");
      return null;
    }
  }

  void dispose() {
    _client.close();
  }

  /// 一直轮询，直到能访问指定接口（返回 true）
  Future<void> waitUntilConnected({
    Duration interval = const Duration(seconds: 2),
  }) async {
    while (true) {
      final success = await _tryPing();
      if (success) return;
      await Future.delayed(interval);
    }
  }

  Future<bool> _tryPing() async {
    try {
      final request = await _client.getUrl(Uri.parse('https://www.baidu.com'));
      final response = await request.close();
      return response.statusCode == 200;
    } catch (_) {
      return false;
    }
  }
}

class DomainLatency {
  final String domain;
  final double? seconds; // null 表示失败
  final bool isSuccess;
  DomainLatency(this.domain, this.seconds, this.isSuccess);
}

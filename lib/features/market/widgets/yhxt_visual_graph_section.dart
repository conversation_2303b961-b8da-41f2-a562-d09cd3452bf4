import 'dart:math' show max, min;

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/logic/sort_color/sort_color_cubit.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';

import '../../../shared/constants/enums.dart';
import '../../../shared/models/route_arguments/trading_arguments.dart';
import '../../../shared/routes/app_router.dart';
import '../../../shared/widgets/shimmer/shimmer_widget.dart';

class YhxtVisualGraphSection extends StatefulWidget {
  final bool isFromHome;

  const YhxtVisualGraphSection({super.key, this.isFromHome = false});

  @override
  State<YhxtVisualGraphSection> createState() => _YhxtVisualGraphSectionState();
}

class _YhxtVisualGraphSectionState extends State<YhxtVisualGraphSection> with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  int _firstVisibleItemIndex = 0;
  static const double _cardHeight = 84;
  final double _itemWidth = 0.32.gsw - 4.gw; // Exactly 1/3 of screen width
  final double _itemSpacing = 8.gw;

  // Variables for snapping behavior
  bool _isScrolling = false;
  int _targetPage = 0;
  final int _itemsPerPage = 3; // We want to show 3 cards per page
  late AnimationController _animationController;
  late Animation<double> _animation;
  final PageController _pageController = PageController(
    viewportFraction: 0.33,
    initialPage: 0,
  );

  // Calculate the total width of a single item including spacing
  double get _totalItemWidth => _itemWidth + _itemSpacing;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller for smooth scrolling
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Add listeners for scroll events
    _scrollController.addListener(_onScroll);

    // Add listener for when scrolling ends to snap to the nearest page
    _scrollController.addListener(_onScrollEnd);

    // Add listener for when page changes
    _pageController.addListener(_onPageChanged);

    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        context.read<IndexTradeCubit>().updateAnimate();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.removeListener(_onScrollEnd);
    _scrollController.dispose();
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onPageChanged() {
    // Sync the PageController with our ScrollController
    if (_scrollController.hasClients && _scrollController.position.maxScrollExtent > 0) {
      _scrollController.position.notifyListeners();
    }
  }

  // This method handles the scrolling logic and updates the current visible index
  void _onScroll() {
    if (!_scrollController.hasClients) return;

    final state = context.read<IndexTradeCubit>().state;
    final itemCount = state.indexStocks.length;
    if (itemCount == 0) return;

    final viewportWidth = _scrollController.position.viewportDimension;
    final scrollOffset = _scrollController.offset;

    // Calculate the current page based on scroll offset

    // Calculate the first visible item index
    final viewportCenter = scrollOffset + (viewportWidth / 2);
    final calculatedIndex = (viewportCenter / _totalItemWidth).floor();
    final safeIndex = max(0, min(calculatedIndex, itemCount - 1));

    if (safeIndex != _firstVisibleItemIndex) {
      setState(() => _firstVisibleItemIndex = safeIndex);
    }
  }

  // This method detects when scrolling ends and snaps to the nearest page
  void _onScrollEnd() {
    if (!_scrollController.hasClients) return;

    // Check if the scroll is idle (not being dragged)
    if (!_scrollController.position.isScrollingNotifier.value) {
      if (_isScrolling) {
        _isScrolling = false;
        _snapToPage();
      }
    } else {
      _isScrolling = true;
    }
  }

  // Snap to the nearest page when scrolling ends
  void _snapToPage() {
    if (!_scrollController.hasClients) return;

    final viewportWidth = _scrollController.position.viewportDimension;
    final scrollOffset = _scrollController.offset;

    // Calculate the target page based on current scroll position
    final itemCount = context.read<IndexTradeCubit>().state.indexStocks.length;
    final maxPages = (itemCount / _itemsPerPage).ceil();

    // Calculate which page we're closest to
    final page = (scrollOffset / viewportWidth).round();
    _targetPage = max(0, min(page, maxPages - 1));

    // Calculate the target offset for the page
    final targetOffset = _targetPage * viewportWidth;

    // Only animate if we're not already at the target
    if ((targetOffset - scrollOffset).abs() > 0.5) {
      // Set up the animation
      _animation = Tween<double>(
        begin: scrollOffset,
        end: targetOffset,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ));

      // Add listener to update scroll position during animation
      _animation.addListener(() {
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(_animation.value);
        }
      });

      // Reset and start the animation
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<IndexTradeCubit, IndexTradeState>(
          listenWhen: (previous, current) {
            // 如果 selectedIndex 越界就直接返回 false
            final safePrev = previous.selectedIndex < previous.indexes.length;
            final safeCurr = current.selectedIndex < current.indexes.length;

            if (!safePrev || !safeCurr) return false;

            return previous.indexes[previous.selectedIndex].lotSize != current.indexes[current.selectedIndex].lotSize ||
                previous.selectedIndex != current.selectedIndex;
          },
          listener: (context, state) {
            if (widget.isFromHome) return;
            context.read<TradingCubit>()
              ..setOrderFraction(orderFraction: OrderFraction.full)
              ..setIndexTrading(isIndexTrading: true);
          },
        ),
        BlocListener<IndexTradeCubit, IndexTradeState>(
          listenWhen: (previous, current) => previous.animate != current.animate,
          listener: (context, state) {
            if (widget.isFromHome) return;
            if (state.animate) {
              _pageController.animateToPage(
                state.selectedIndex,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
              context.read<IndexTradeCubit>().updateAnimate(animate: false);
              setState(() {
                _firstVisibleItemIndex = state.selectedIndex;
              });
            }
          },
        ),
      ],
      child: BlocBuilder<IndexTradeCubit, IndexTradeState>(
        bloc: getIt<IndexTradeCubit>(),
        builder: (context, state) {
          int itemCount = state.indexStocks.length;
          if (state.status == DataStatus.loading) {
            return _buildLoading();
          }

          if (state.status.isFailed || itemCount == 0) {
            return const Center(child: TableEmptyWidget());
          }

          return SizedBox(
            height: _cardHeight.gh,
            child: PageView.builder(
              key: PageStorageKey('visual_graph//${widget.isFromHome}'),
              controller: _pageController,
              itemCount: itemCount,
              onPageChanged: (index) {
                setState(() {
                  _firstVisibleItemIndex = index;
                });
              },
              pageSnapping: true,
              padEnds: false,
              // physics: const CustomPageViewScrollPhysics(),
              itemBuilder: (context, i) => Padding(
                padding: EdgeInsets.only(right: _itemSpacing),
                child: _buildStockCard(context, state, i),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStockCard(BuildContext context, IndexTradeState state, int i) {
    final data = state.indexStocks;
    final isSelected = i == state.selectedIndex;

    final market = getMainMarketType(data[i].stockInfo.data?.market ?? '');
    // Extract stock data
    final _IndexStockData stockData = _extractStockData(context, data, i, market);

    return GestureDetector(
      onTap: () => _handleCardTap(data, i),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        width: _itemWidth,
        padding: EdgeInsets.symmetric(horizontal: 4.gw),
        margin: _calculateCardMargin(isSelected, i),
        decoration: BoxDecoration(
            image: DecorationImage(
          image: AssetImage(stockData.bgImgPath),
          fit: BoxFit.fill,
          alignment: Alignment.topCenter,
        )),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 8.gw),
            // Stock name
            Text(
              stockData.name,
              style: TextStyle(color: Colors.black),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 5.gw),
            // Price
            FlipText(
              stockData.price,
              style: context.textTheme.primary.w500.copyWith(
                color: stockData.textColor,
              ),
              fractionDigits: 3,
            ),
            SizedBox(height: 6.gw),
            // Change and percentage
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: 4,
              children: [
                // Price change
                FlipText(
                  stockData.change,
                  prefix: stockData.change >= 0 ? '+' : '',
                  defaultFontFamily: false,
                  style: context.textTheme.primary.fs11.copyWith(
                    color: stockData.textColor,
                  ),
                ),
                // Percentage change
                FlipText(
                  stockData.gainPercentage,
                  suffix: '%',
                  defaultFontFamily: false,
                  style: context.textTheme.primary.fs11.w500.copyWith(
                    color: stockData.textColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Extract stock data from the state
  _IndexStockData _extractStockData(BuildContext context, List<IndexStockInfo> data, int index, MainMarketType market) {
    // Default values
    String name = '';
    double price = 0.0, gain = 0.00, change = 0.0;
    Color textColor = Colors.grey;
    String bgImgPath = '';

    if (data.isNotEmpty && index < data.length) {
      final stockInfo = data[index].stockInfo.data;
      if (stockInfo != null) {
        name = stockInfo.name ?? '';
        price = stockInfo.latestPrice ?? 0.0;
        change = price - (stockInfo.close ?? 0.0);

        // Avoid division by zero
        final closePrice = stockInfo.close ?? 0.0;
        gain = closePrice != 0.0 ? change / closePrice : 0.0;

        textColor = gain.getValueColor(context);

        final bgGreenPath = "assets/images/bg_home_stock_card_green.png";
        final bgRedPath = "assets/images/bg_home_stock_card_red.png";
        final isGain = gain > 0;
        final marketColor = context.watch<SortColorCubit>().state.marketColor;
        final isRedUp = marketColor == MarketColor.redUpGreenDown;
        bgImgPath = switch ((isRedUp, isGain)) {
          (true, true) => bgRedPath, // 红涨 Red Up
          (true, false) => bgGreenPath, // 绿跌 Green Down
          (false, true) => bgGreenPath, // 绿涨 Green Up
          (false, false) => bgRedPath, // 红跌 Red Down
        };
      }
    }

    return _IndexStockData(
      name: name,
      price: price,
      change: change,
      gainPercentage: gain * 100,
      textColor: textColor,
      bgImgPath: bgImgPath,
    );
  }

  /// Calculate card margin based on selection state and position
  EdgeInsets _calculateCardMargin(bool isSelected, int index) {
    return EdgeInsets.only(
      right: isSelected && !widget.isFromHome ? 0.gw : 1.gw,
      left: 0,
      bottom: isSelected && !widget.isFromHome ? 0 : 2.gw,
      top: isSelected && !widget.isFromHome ? 0 : 2.gw,
    );
  }

  void _handleCardTap(List<IndexStockInfo> data, int i) {
    if (widget.isFromHome) {
      AuthUtils.verifyAuth(
        () {
          StockInfoData? stockInfo = data[i].stockInfo.data;
          getIt<NavigatorService>().push(
            AppRouter.routeTradingCenter,
            arguments: TradingArguments(
              instrumentInfo: stockInfo!.instrumentInfo,
              selectedIndex: TradeTabType.Quotes.index,
              shouldNavigateToIndex: widget.isFromHome,
              isIndexTrading: true,
            ),
          );
        },
      );
    }
    context.read<IndexTradeCubit>().updateSelectedIndex(i);

    if (!widget.isFromHome) {
      context.read<TradingCubit>()
        ..setSelectedPositionOpenShort(null)
        ..setSelectedPositionSellLong(null);
    }
  }

  Widget _buildLoading() {
    final double itemWidth = 0.32.gsw - 4.gw;
    return SizedBox(
      height: 84.gh,
      child: ListView.separated(
        itemCount: 3,
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 6),
        scrollDirection: Axis.horizontal,
        separatorBuilder: (_, __) => const SizedBox(width: 8),
        itemBuilder: (_, __) => ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: ShimmerWidget(
            height: 84.gh,
            width: itemWidth,
          ),
        ),
      ),
    );
  }
}

/// Helper class to store stock card data
class _IndexStockData {
  final String name;
  final double price;
  final double change;
  final double gainPercentage;
  final Color textColor;
  final String bgImgPath;

  _IndexStockData({
    required this.name,
    required this.price,
    required this.change,
    required this.gainPercentage,
    required this.textColor,
    required this.bgImgPath,
  });
}

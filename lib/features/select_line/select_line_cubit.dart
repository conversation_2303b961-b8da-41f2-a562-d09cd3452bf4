import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/utils/host_util.dart';

import 'select_line_state.dart';

class SelectLineCubit extends Cubit<SelectLineState> {
  final HostUtil _hostUtil;
  Timer? _timer;
  bool _running = false;

  SelectLineCubit({HostUtil? hostUtil})
      : _hostUtil = hostUtil ?? HostUtil(),
        super(const SelectLineState()) {
    final items = _hostUtil.hostList.map((e) => LineItem(domain: e, seconds: null, isSuccess: false)).toList();

    emit(state.copyWith(items: items, selected: _hostUtil.currentHost));
  }

  Future<void> refreshAndTest() async {
    if (_running) return;
    _running = true;
    final domains = _hostUtil.hostList;
    final results = await _hostUtil.testHosts(domains);
    // 保持原始顺序展示，不做排序
    final items = results.map((e) => LineItem(domain: e.domain, seconds: e.seconds, isSuccess: e.isSuccess)).toList();
    // 仅刷新显示
    emit(state.copyWith(items: items));
    _running = false;
  }

  Future<void> select(String domain) async {
    emit(state.copyWith(selected: domain));
    await _hostUtil.selectHost(domain);
  }

  void startAutoTest({Duration interval = const Duration(seconds: 3)}) {
    _timer?.cancel();
    _timer = Timer.periodic(interval, (_) => refreshAndTest());
  }

  Future<void> seedInitial() async {
    List<String> domains = _hostUtil.hostList;
    if (domains.isEmpty) {
      domains = await _hostUtil.refreshOssDomains();
    }
    final items = domains.map((e) => LineItem(domain: e, seconds: null, isSuccess: true)).toList();
    final selected = _hostUtil.currentHost ?? (domains.isNotEmpty ? domains.first : null);
    emit(state.copyWith(loading: false, items: items, selected: selected));
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}

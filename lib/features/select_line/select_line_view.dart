import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

import 'select_line_cubit.dart';
import 'select_line_state.dart';

class SelectLinePage extends StatelessWidget {
  const SelectLinePage({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<SelectLineCubit>();
    return Scaffold(
      appBar: AppBar(
        title: Text('line_selection'.tr()),
      ),
      body: BlocBuilder<SelectLineCubit, SelectLineState>(
        builder: (context, state) {
          if (state.loading && state.items.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }
          return ListView.separated(
            itemCount: state.items.length,
            separatorBuilder: (_, __) => Divider(height: 0.5, color: context.theme.dividerColor),
            itemBuilder: (context, index) {
              final item = state.items[index];
              final isSelected = state.selected == item.domain;
              final latencyText = item.seconds != null ? '${item.seconds!.toStringAsFixed(3)}s' : '--';
              return ListTile(
                // title: Text(item.domain, style: const TextStyle(fontSize: 14)),
                title: Text("line No:${index+1}", style: context.textTheme.title),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(latencyText, style: context.textTheme.title),
                    const SizedBox(width: 10),
                    Icon(
                      isSelected ? Icons.check_circle : Icons.radio_button_unchecked,
                      color: isSelected ? context.theme.primaryColor : context.colorTheme.border,
                    ),
                  ],
                ),
                onTap: () => cubit.select(item.domain),
              );
            },
          );
        },
      ),
    );
  }
}

import 'package:equatable/equatable.dart';

class SelectLineState extends Equatable {
  final bool loading;
  final List<LineItem> items;
  final String? selected;
  const SelectLineState({this.loading = false, this.items = const [], this.selected});

  SelectLineState copyWith({bool? loading, List<LineItem>? items, String? selected}) {
    return SelectLineState(
      loading: loading ?? this.loading,
      items: items ?? this.items,
      selected: selected ?? this.selected,
    );
  }

  @override
  List<Object?> get props => [loading, items, selected];
}

class LineItem extends Equatable {
  final String domain;
  final double? seconds; // null 表示失败
  final bool isSuccess;
  const LineItem({required this.domain, required this.seconds, required this.isSuccess});

  @override
  List<Object?> get props => [domain, seconds, isSuccess];
}

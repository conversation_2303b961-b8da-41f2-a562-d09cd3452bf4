import 'package:flutter/material.dart';
import 'package:gp_stock_app/shared/mixins/chat_button_mixin.dart';
import 'package:gp_stock_app/shared/widgets/webview/webview_native.dart';

class CommonWebViewPage extends StatefulWidget {
  final String? title;
  final String url;

  const CommonWebViewPage({
    super.key,
    this.title,
    required this.url,
  });

  @override
  State createState() => _CommonWebViewPageState();
}

class _CommonWebViewPageState extends State<CommonWebViewPage> with HideFloatButtonRouteAwareMixin {
  final webViewKey = UniqueKey();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Text(widget.title ?? ''),
        ),
        bottomNavigationBar: Container(
          height:  MediaQuery.of(context).padding.bottom > 0 ? 20 : 0,
          color: Colors.white,
        ),
        body: WebView(initialUrl: widget.url));
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../shared/app/utilities/easy_loading.dart';
import '../../../shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class StyleDHomeMenu extends StatefulWidget {
  final Color? color;
  final bool isOriginalColor;
  final bool showVip;
  final bool showAI;

  const StyleDHomeMenu({
    super.key,
    this.color,
    this.isOriginalColor = false,
    this.showVip = false,
    this.showAI = true,
  });

  @override
  State<StyleDHomeMenu> createState() => _StyleDHomeMenuState();
}

class _StyleDHomeMenuState extends State<StyleDHomeMenu> with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<_Action> data = [
      _Action(
        icon: 'assets/svg/home/<USER>',
        title: 'homeH5Title',
        onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeAboutUs)),
      ),
      _Action(
        icon: 'assets/svg/home/<USER>',
        title: 'homeH2Title',
        onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeMissionCenter)),
      ),
      if (widget.showVip) ...[
        _Action(
          icon: 'assets/svg/home/<USER>',
          title: 'vip',
          onTap: () =>
              AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeMissionCenter, arguments: true)),
        ),
      ],
      if (widget.showAI) ...[
        _Action(
          icon: 'assets/svg/home/<USER>',
          title: 'aiAnalysis',
          onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeAIChat)),
        ),
      ],
      _Action(
        icon: 'assets/svg/home/<USER>',
        title: 'homeH4Title',
        onTap: () => _launchServiceUrl(context),
      ),
    ];

    return Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          color: widget.color ?? context.theme.appBarTheme.backgroundColor,
          boxShadow: const [
            BoxShadow(
              color: Color(0x0F354677),
              offset: Offset(0, 4),
              blurRadius: 10,
              spreadRadius: 0,
            ),
          ],
        ),
        child: AnimationLimiter(
            child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            for (int i = 0; i < data.length; i++) ...[
              Expanded(
                child: AnimationConfiguration.staggeredList(
                  position: i,
                  duration: const Duration(milliseconds: 400),
                  child: SlideAnimation(
                    horizontalOffset: 50.0,
                    child: FadeInAnimation(
                      child: _buildMenuButton(context, model: data[i]),
                    ),
                  ),
                ),
              ),
              if (i < data.length - 1) SizedBox(width: 5.gw),
            ],
          ],
        )));
  }

  void _launchServiceUrl(BuildContext context) {
    AuthUtils.verifyAuth(() {
      final state = context.read<SysSettingsCubit>().state;
      state.maybeWhen(
        loaded: (sysSettings, _) {
          final serviceUrl = sysSettings.service;
          if (serviceUrl != null && serviceUrl.isNotEmpty) {
            launchUrl(Uri.parse(serviceUrl), mode: LaunchMode.inAppBrowserView);
          } else {
            GPEasyLoading.showToast('something_went_wrong'.tr());
          }
        },
        orElse: () => GPEasyLoading.showToast('something_went_wrong'.tr()),
      );
    });
  }

  Widget _buildMenuButton(
    BuildContext context, {
    required _Action model,
  }) {
    return Bounceable(
      onTap: model.onTap,
      child: Column(
        children: [
          IconHelper.loadAsset(
            model.icon,
            width: 30.gw,
            height: 30.gh,
            color: widget.isOriginalColor ? null : context.theme.primaryColor,
          ),
          Text(
            model.title.tr(),
            style: context.textTheme.primary.fs12,
            textAlign: TextAlign.center,
          )
        ],
      ),
    );
  }
}

class _Action {
  final String icon;
  final String title;
  final VoidCallback onTap;

  _Action({
    required this.icon,
    required this.title,
    required this.onTap,
  });
}
